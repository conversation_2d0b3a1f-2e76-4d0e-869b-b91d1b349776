<a href="#" data-bs-toggle="modal" data-bs-target="@($"#{Id}")">
    @ButtonContent
</a>

<div class="modal fade" id="@Id" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-dark">@Title</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    @ChildContent
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter] public RenderFragment? ChildContent { get; set; }
    [Parameter] public RenderFragment? ButtonContent { get; set; }
    [Parameter] public string? Title { get; set; }
    [Parameter] public string? Id { get; set; }
}
