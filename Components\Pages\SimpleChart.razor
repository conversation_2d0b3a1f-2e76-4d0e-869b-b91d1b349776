@page "/simple-chart"
@using ApexCharts
@using System.Linq
@rendermode InteractiveServer

<PageTitle>簡單統計圖表</PageTitle>
<MyPageTitle Title="簡單統計圖表"></MyPageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">銷售數據統計</h5>
                </div>
                <div class="card-body">
                    <!-- 圖表類型切換按鈕 -->
                    <div class="text-center mt-3 mb-3">
                        <div class="btn-group" role="group">
                            <button type="button"
                                    class="btn @(currentChartType == SeriesType.Bar ? "btn-primary" : "btn-outline-primary")"
                                    @onclick="async () => await ChangeChartType(SeriesType.Bar)">
                                <i class="fa-solid fa-chart-column"></i> 長條圖
                            </button>
                            <button type="button"
                                    class="btn @(currentChartType == SeriesType.Line ? "btn-primary" : "btn-outline-primary")"
                                    @onclick="async () => await ChangeChartType(SeriesType.Line)">
                                <i class="fa-solid fa-chart-line"></i> 折線圖
                            </button>
                        </div>
                    </div>

                    <!-- 互動視窗按鈕 -->
                    <BS5Modal Id="chartModal" Title="@($"銷售數據統計 - 詳細圖表")">
                        <ButtonContent>
                            <i class="fa-solid fa-magnifying-glass-plus"></i> 按我唷
                        </ButtonContent>

                        <ChildContent>
                            <!-- 互動視窗內的統計圖表 -->
                            <div>
                                @if (shouldRenderModalChart)
                                {
                                    <ApexChart TItem="SalesData"
                                               Title="月度銷售統計 - 詳細檢視"
                                               Options="modalChartOptions">
                                        <ApexPointSeries TItem="SalesData"
                                                         Items="salesData"
                                                         SeriesType="currentChartType"
                                                         Name="銷售額"
                                                         XValue="@(e => e.Month)"
                                                         YValue="@(e => e.Amount)" />
                                    </ApexChart>
                                }
                            </div>
                        </ChildContent>
                    </BS5Modal>
                    <!-- 統計圖表 -->
                    <div>
                        @if (shouldRenderChart)
                        {
                            <ApexChart TItem="SalesData"
                                       Title="月度銷售統計"
                                       Options="chartOptions">
                                <ApexPointSeries TItem="SalesData"
                                                 Items="salesData"
                                                 SeriesType="currentChartType"
                                                 Name="銷售額"
                                                 XValue="@(e => e.Month)"
                                                 YValue="@(e => e.Amount)" />
                            </ApexChart>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter] public string? Title { get; set; }

    // 數據模型
    public class SalesData
    {
        public string Month { get; set; } = "";
        public decimal Amount { get; set; }
    }

    // 數據和變數
    private List<SalesData> salesData = new();
    private SeriesType currentChartType = SeriesType.Bar;

    // 控制圖表渲染
    private bool shouldRenderChart = true;
    private bool shouldRenderModalChart = true;

    // 圖表選項
    private ApexChartOptions<SalesData> chartOptions = new();
    private ApexChartOptions<SalesData> modalChartOptions = new();

    // 表格數據
    private List<Dictionary<string, object>> tableData = new();
    private List<string> tableColumns = new() { "月份", "銷售額" };

    protected override void OnInitialized()
    {
        GenerateData();
        SetupChartOptions();
        GenerateTableData();
    }

    private void GenerateData()
    {
        var random = new Random();
        var months = new[] { "1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月" };
        
        salesData = months.Select(month => new SalesData
        {
            Month = month,
            Amount = random.Next(50000, 200000)
        }).ToList();
    }

    private void GenerateTableData()
    {
        tableData = salesData.Select(item => new Dictionary<string, object>
        {
            { "月份", item.Month },
            { "銷售額", item.Amount }
        }).ToList();
    }

    private async Task ChangeChartType(SeriesType newType)
    {
        currentChartType = newType;

        // 暫時隱藏主頁面和互動視窗圖表
        shouldRenderChart = false;
        shouldRenderModalChart = false;
        StateHasChanged();

        // 等待一個短暫的延遲後重新顯示圖表
        await Task.Delay(50);
        shouldRenderChart = true;
        shouldRenderModalChart = true;
        StateHasChanged();
    }

    private void SetupChartOptions()
    {
        // 主頁面圖表選項
        chartOptions = new ApexChartOptions<SalesData>
        {
            Theme = new Theme { Mode = Mode.Light },
            Chart = new Chart
            {
                Toolbar = new Toolbar { Show = true },
                Background = "transparent"
            },
            Colors = new List<string> { "#3498db" },
            DataLabels = new DataLabels { Enabled = true },
            Xaxis = new XAxis
            {
                Title = new AxisTitle { Text = "月份" }
            },
            Yaxis = new List<YAxis>
            {
                new YAxis
                {
                    Title = new AxisTitle { Text = "銷售額 (NT$)" },
                    Labels = new YAxisLabels
                    {
                        Formatter = "function(value) { return 'NT$' + value.toLocaleString(); }"
                    }
                }
            }
        };

        // 互動視窗圖表選項
        modalChartOptions = new ApexChartOptions<SalesData>
        {
            Theme = new Theme { Mode = Mode.Light },
            Chart = new Chart
            {
                Toolbar = new Toolbar { Show = true },
                Background = "transparent"
            },
            Colors = new List<string> { "#e74c3c" },
            DataLabels = new DataLabels { Enabled = true },
            Xaxis = new XAxis
            {
                Title = new AxisTitle { Text = "月份" }
            },
            Yaxis = new List<YAxis>
            {
                new YAxis
                {
                    Title = new AxisTitle { Text = "銷售額 (NT$)" },
                    Labels = new YAxisLabels
                    {
                        Formatter = "function(value) { return 'NT$' + value.toLocaleString(); }"
                    }
                }
            }
        };
    }
}
